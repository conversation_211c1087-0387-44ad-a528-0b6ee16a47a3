"use client";

import { gsap } from "gsap";
import { useEffect, useRef } from "react";
import { ScrollTrigger } from "gsap/ScrollTrigger";

if (typeof window !== "undefined") {
  gsap.registerPlugin(ScrollTrigger);
}

const FeatureIcon1 = () => (
  <svg viewBox="0 0 202 108" className="h-56 w-56 sm:h-72 sm:w-72">
    <g>
      <path d="M 0 0 L 202 0 L 202 108 L 0 108 Z" fill="transparent"></path>
      <path
        d="M 23.625 43.547 C 23.625 43.547 20.875 41.359 20.955 37.643 C 21.032 33.926 24.25 31.873 24.25 31.873 C 24.25 31.873 26.584 34.23 26.507 38.048 C 26.422 41.861 23.625 43.547 23.625 43.547 Z M 34.512 46.06 C 31.515 48.26 28.151 47.264 28.151 47.264 C 28.151 47.264 27.923 44.009 30.991 41.748 C 34.067 39.492 37.333 40.046 37.333 40.046 C 37.333 40.046 37.503 43.864 34.513 46.06 Z M 33.054 48.731 C 33.054 48.731 33.718 52.495 31.044 55.069 C 28.364 57.636 24.89 57.093 24.89 57.093 C 24.89 57.093 24.241 53.887 26.991 51.247 C 29.742 48.613 33.054 48.731 33.054 48.731 Z M 31.954 63.196 C 29.526 66.002 26.024 65.785 26.024 65.785 C 26.024 65.785 25.074 62.664 27.568 59.771 C 30.065 56.885 33.369 56.696 33.369 56.696 C 33.369 56.696 34.379 60.379 31.954 63.196 Z M 33.863 71.038 C 32.343 74.432 28.924 75.225 28.924 75.225 C 28.924 75.225 27.119 72.505 28.672 69.024 C 30.233 65.542 33.354 64.406 33.354 64.406 C 33.354 64.406 35.378 67.641 33.863 71.038 Z M 30.277 38.706 C 30.277 38.706 29.202 35.364 31.324 32.316 C 33.452 29.266 37.27 29.343 37.27 29.343 C 37.27 29.343 37.901 32.605 35.72 35.728 C 33.54 38.86 30.277 38.706 30.277 38.706 Z M 21.924 44.79 C 22.969 48.453 20.792 50.894 20.792 50.894 C 20.792 50.894 17.523 49.618 16.502 46.042 C 15.482 42.468 17.954 39.554 17.954 39.554 C 17.954 39.554 20.876 41.12 21.924 44.79 Z M 20.03 53.151 C 21.86 56.491 20.28 59.354 20.28 59.354 C 20.28 59.354 16.808 58.837 15.02 55.577 C 13.234 52.32 14.994 48.926 14.994 48.926 C 14.994 48.926 18.197 49.804 20.03 53.151 Z M 19.415 61.156 C 22.171 63.785 21.529 66.991 21.529 66.991 C 21.529 66.991 18.063 67.554 15.374 64.986 C 12.689 62.424 13.342 58.656 13.342 58.656 C 13.342 58.656 16.65 58.522 19.415 61.156 Z M 22.125 70.188 C 25.175 72.473 24.92 75.723 24.92 75.723 C 24.92 75.723 21.548 76.699 18.573 74.478 C 15.596 72.251 15.803 68.432 15.803 68.432 C 15.803 68.432 19.069 67.91 22.125 70.188 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 28.608 39.567 L 29.844 40.907 C 29.289 41.417 16.526 53.767 30.551 79.781 L 28.954 80.644 C 14.219 53.327 28.464 39.705 28.608 39.567 Z M 178.376 43.547 C 178.376 43.547 181.127 41.359 181.047 37.643 C 180.969 33.926 177.751 31.873 177.751 31.873 C 177.751 31.873 175.417 34.23 175.494 38.048 C 175.579 41.861 178.376 43.547 178.376 43.547 Z M 167.489 46.06 C 170.486 48.26 173.85 47.264 173.85 47.264 C 173.85 47.264 174.078 44.008 171.01 41.748 C 167.934 39.492 164.668 40.046 164.668 40.046 C 164.668 40.046 164.497 43.864 167.489 46.06 Z M 168.947 48.731 C 168.947 48.731 168.283 52.495 170.956 55.069 C 173.637 57.636 177.111 57.093 177.111 57.093 C 177.111 57.093 177.759 53.887 175.01 51.247 C 172.259 48.613 168.947 48.731 168.947 48.731 Z M 170.047 63.196 C 172.475 66.002 175.977 65.785 175.977 65.785 C 175.977 65.785 176.926 62.664 174.433 59.771 C 171.936 56.885 168.632 56.695 168.632 56.695 C 168.632 56.695 167.623 60.379 170.047 63.195 Z M 168.138 71.038 C 169.657 74.432 173.077 75.225 173.077 75.225 C 173.077 75.225 174.882 72.505 173.329 69.024 C 171.767 65.542 168.647 64.406 168.647 64.406 C 168.647 64.406 166.623 67.641 168.138 71.038 Z M 171.724 38.706 C 171.724 38.706 172.798 35.364 170.677 32.316 C 168.549 29.266 164.73 29.343 164.73 29.343 C 164.73 29.343 164.1 32.605 166.282 35.728 C 168.462 38.86 171.724 38.706 171.724 38.706 Z M 180.077 44.79 C 179.032 48.453 181.209 50.894 181.209 50.894 C 181.209 50.894 184.478 49.618 185.499 46.042 C 186.519 42.468 184.047 39.554 184.047 39.554 C 184.047 39.554 181.125 41.12 180.077 44.79 Z M 181.971 53.151 C 180.141 56.491 181.72 59.354 181.72 59.354 C 181.72 59.354 185.193 58.837 186.981 55.577 C 188.767 52.32 187.007 48.926 187.007 48.926 C 187.007 48.926 183.804 49.804 181.971 53.151 Z M 182.586 61.156 C 179.83 63.785 180.472 66.991 180.472 66.991 C 180.472 66.991 183.938 67.554 186.627 64.986 C 189.312 62.424 188.659 58.656 188.659 58.656 C 188.659 58.656 185.351 58.522 182.586 61.156 Z M 179.876 70.188 C 176.826 72.473 177.081 75.723 177.081 75.723 C 177.081 75.723 180.453 76.699 183.428 74.477 C 186.405 72.251 186.198 68.432 186.198 68.432 C 186.198 68.432 182.932 67.91 179.876 70.188 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 173.393 39.567 L 172.157 40.907 C 172.712 41.417 185.475 53.767 171.45 79.781 L 173.047 80.644 C 187.782 53.327 173.538 39.705 173.393 39.567 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 53.197 67.398 C 54.087 68.188 54.533 69.128 54.533 70.219 C 54.533 71.317 54.088 72.26 53.197 73.049 C 52.305 73.837 51.217 74.231 49.93 74.231 C 48.648 74.231 47.56 73.837 46.67 73.049 C 45.779 72.26 45.333 71.317 45.333 70.219 C 45.333 69.129 45.779 68.189 46.67 67.399 C 47.56 66.609 48.648 66.213 49.93 66.213 C 51.217 66.213 52.306 66.608 53.197 67.398 Z M 49.93 67.072 C 48.988 67.072 48.172 67.377 47.484 67.988 C 46.796 68.598 46.452 69.342 46.452 70.219 C 46.452 71.104 46.796 71.85 47.484 72.459 C 48.172 73.068 48.988 73.372 49.93 73.372 C 50.877 73.372 51.694 73.068 52.382 72.459 C 53.071 71.85 53.415 71.103 53.415 70.219 C 53.415 69.342 53.071 68.599 52.382 67.988 C 51.694 67.378 50.877 67.072 49.93 67.072 Z M 65.113 66.322 L 65.113 67.175 L 61.935 67.175 L 61.935 69.806 L 65.113 69.806 L 65.113 70.66 L 61.935 70.66 L 61.935 74.144 L 60.823 74.144 L 60.823 66.322 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 49.93 66.571 C 48.728 66.571 47.727 66.939 46.906 67.667 C 46.084 68.396 45.69 69.242 45.69 70.22 C 45.69 71.205 46.084 72.054 46.906 72.78 C 47.726 73.506 48.728 73.873 49.931 73.873 C 51.138 73.873 52.141 73.506 52.961 72.78 C 53.783 72.054 54.177 71.205 54.177 70.22 C 54.177 69.242 53.783 68.396 52.961 67.667 C 52.141 66.939 51.138 66.571 49.931 66.571 Z M 46.434 67.131 C 47.396 66.278 48.568 65.856 49.931 65.856 C 51.296 65.856 52.471 66.278 53.433 67.13 C 54.393 67.981 54.891 69.016 54.891 70.22 C 54.891 71.43 54.394 72.468 53.433 73.317 C 52.471 74.168 51.296 74.589 49.931 74.589 C 48.569 74.589 47.396 74.168 46.434 73.317 C 45.474 72.467 44.977 71.43 44.977 70.22 C 44.977 69.016 45.474 67.981 46.434 67.13 Z M 60.466 65.964 L 65.47 65.964 L 65.47 67.534 L 62.292 67.534 L 62.292 69.448 L 65.47 69.448 L 65.47 71.018 L 62.292 71.018 L 62.292 74.502 L 60.466 74.502 Z M 61.18 66.68 L 61.18 73.786 L 61.579 73.786 L 61.579 70.302 L 64.756 70.302 L 64.756 70.164 L 61.58 70.164 L 61.58 66.818 L 64.757 66.818 L 64.757 66.68 Z M 47.248 67.72 C 48.004 67.05 48.904 66.714 49.931 66.714 C 50.961 66.714 51.864 67.05 52.619 67.72 C 53.379 68.395 53.772 69.234 53.772 70.22 C 53.772 71.211 53.38 72.054 52.619 72.728 C 51.863 73.396 50.961 73.73 49.931 73.73 C 48.904 73.73 48.004 73.396 47.249 72.728 C 46.487 72.054 46.095 71.211 46.095 70.22 C 46.095 69.234 46.488 68.395 47.248 67.72 Z M 49.931 67.43 C 49.073 67.43 48.342 67.705 47.721 68.256 C 47.105 68.803 46.809 69.451 46.809 70.219 C 46.809 70.996 47.105 71.647 47.721 72.191 C 48.341 72.741 49.073 73.014 49.931 73.014 C 50.793 73.014 51.526 72.74 52.147 72.191 C 52.762 71.647 53.059 70.996 53.059 70.219 C 53.059 69.451 52.763 68.803 52.146 68.256 C 51.526 67.706 50.793 67.43 49.931 67.43 Z M 156.309 67.275 C 156.309 65.235 155.597 63.607 154.175 62.39 C 152.787 61.209 151.24 60.672 149.533 60.779 L 148.412 60.859 L 147.345 60.994 L 148.492 56.054 L 154.948 56.054 L 154.948 54.954 L 147.638 54.954 L 145.744 62.39 C 146.402 62.14 147.034 62.014 147.638 62.014 C 147.905 61.942 148.119 61.906 148.279 61.906 L 148.679 61.906 L 149.533 61.88 C 151.009 61.826 152.307 62.282 153.428 63.249 C 154.619 64.269 155.215 65.611 155.215 67.275 C 155.215 68.975 154.681 70.398 153.614 71.544 C 152.547 72.707 151.187 73.289 149.533 73.289 C 148.145 73.289 147.016 72.967 146.144 72.322 C 145.451 71.803 144.846 71.016 144.33 69.96 L 143.316 70.363 C 144.437 73.047 146.509 74.389 149.533 74.389 C 151.542 74.389 153.179 73.719 154.441 72.376 C 155.686 71.052 156.309 69.352 156.309 67.276 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 147.085 54.238 L 155.663 54.238 L 155.663 56.77 L 149.059 56.77 L 148.273 60.156 L 148.343 60.147 L 149.485 60.065 L 149.489 60.065 C 151.393 59.945 153.119 60.552 154.637 61.845 L 154.638 61.845 C 156.235 63.211 157.023 65.047 157.023 67.275 C 157.023 69.507 156.349 71.391 154.961 72.867 C 153.551 74.367 151.719 75.106 149.533 75.106 C 147.915 75.106 146.5 74.746 145.324 73.984 C 144.147 73.222 143.265 72.091 142.659 70.639 L 142.378 69.966 L 144.682 69.051 L 144.972 69.645 C 145.454 70.633 145.994 71.316 146.57 71.748 C 147.288 72.278 148.256 72.573 149.533 72.573 C 150.999 72.573 152.165 72.067 153.09 71.059 L 153.092 71.057 L 153.094 71.055 C 154.026 70.055 154.502 68.811 154.502 67.275 C 154.502 65.807 153.987 64.669 152.965 63.793 L 152.963 63.792 C 151.979 62.942 150.856 62.548 149.559 62.595 L 149.556 62.595 L 148.691 62.623 L 148.279 62.623 C 148.211 62.623 148.067 62.64 147.824 62.706 L 147.733 62.73 L 147.639 62.73 C 147.134 62.73 146.588 62.835 145.998 63.06 L 144.713 63.548 Z M 107.096 59.855 C 107.096 58.341 106.556 57.122 105.477 56.195 C 104.397 55.252 103.106 54.78 101.602 54.78 C 99.85 54.78 98.443 55.368 97.382 56.543 C 96.585 57.451 96.125 58.368 96.002 59.293 L 97.196 59.293 C 97.391 58.475 97.736 57.789 98.231 57.237 C 99.045 56.311 100.168 55.848 101.602 55.848 C 102.735 55.835 103.832 56.245 104.68 56.997 C 105.565 57.78 106.007 58.733 106.007 59.855 C 106.007 61.012 105.636 62.125 104.893 63.193 L 95.603 74.009 L 107.069 74.009 L 107.069 72.914 L 97.992 72.914 L 103.433 66.718 C 104.091 65.963 104.737 65.197 105.37 64.421 C 106.521 62.926 107.096 61.403 107.096 59.854 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 96.853 56.062 C 98.065 54.72 99.673 54.064 101.602 54.064 C 103.268 54.064 104.727 54.592 105.943 55.654 C 107.19 56.724 107.809 58.148 107.809 59.854 C 107.809 61.594 107.16 63.266 105.936 64.858 L 105.93 64.866 L 105.924 64.874 C 105.286 65.656 104.635 66.428 103.971 67.189 L 103.969 67.191 L 99.572 72.198 L 107.783 72.198 L 107.783 74.725 L 94.047 74.725 L 104.328 62.754 C 104.984 61.799 105.294 60.837 105.294 59.854 C 105.294 58.952 104.95 58.191 104.211 57.536 C 103.493 56.898 102.562 56.551 101.602 56.564 C 100.336 56.564 99.422 56.964 98.766 57.711 L 98.764 57.713 L 98.762 57.716 C 98.356 58.168 98.061 58.741 97.89 59.459 L 97.76 60.009 L 95.186 60.009 L 95.294 59.199 C 95.442 58.096 95.984 57.053 96.846 56.069 L 96.849 56.066 Z M 128.288 57.542 C 130.323 59.594 131.34 62.036 131.34 64.87 C 131.34 67.722 130.323 70.171 128.288 72.218 C 126.254 74.265 123.768 75.289 120.831 75.289 C 117.903 75.289 115.422 74.266 113.388 72.219 C 111.353 70.171 110.336 67.722 110.336 64.869 C 110.336 62.036 111.353 59.594 113.388 57.542 C 115.422 55.49 117.903 54.464 120.831 54.464 C 123.768 54.464 126.254 55.49 128.288 57.542 Z M 120.831 56.695 C 118.68 56.695 116.818 57.488 115.247 59.074 C 113.675 60.66 112.89 62.592 112.89 64.87 C 112.89 67.166 113.675 69.105 115.247 70.686 C 116.818 72.268 118.68 73.058 120.831 73.058 C 122.992 73.058 124.858 72.268 126.429 70.686 C 128.001 69.106 128.786 67.166 128.786 64.87 C 128.786 62.592 128.001 60.66 126.429 59.074 C 124.858 57.488 122.992 56.695 120.831 56.695 Z M 138.362 74.105 L 138.362 54.954 L 134.764 54.954 L 134.044 56.054 L 137.269 56.054 L 137.269 74.104 L 138.362 74.104 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 134.378 54.238 L 139.076 54.238 L 139.076 74.821 L 136.556 74.821 L 136.556 56.769 L 132.722 56.769 Z M 48.056 61.21 C 48.568 61.21 49.016 61.04 49.399 60.702 C 49.77 60.388 49.981 59.924 49.973 59.438 C 49.973 58.934 49.782 58.512 49.398 58.171 C 49.031 57.837 48.552 57.655 48.056 57.661 L 46.446 57.661 L 46.446 61.21 Z M 46.446 55.09 L 46.446 56.807 L 48.228 56.807 C 48.469 56.785 48.656 56.715 48.787 56.595 C 48.967 56.439 49.067 56.209 49.057 55.97 C 49.062 55.728 48.959 55.497 48.775 55.34 C 48.601 55.179 48.372 55.089 48.135 55.09 L 46.445 55.09 Z M 50.237 57.6 C 50.811 58.108 51.097 58.72 51.097 59.438 C 51.097 60.148 50.801 60.766 50.207 61.292 C 49.727 61.719 49.121 61.977 48.387 62.064 L 45.333 62.064 L 45.333 54.24 L 48.142 54.24 C 48.719 54.24 49.202 54.412 49.592 54.757 C 49.982 55.101 50.176 55.505 50.176 55.969 C 50.176 56.437 49.986 56.837 49.604 57.17 C 49.74 57.2 49.951 57.342 50.237 57.6 Z M 63.162 54.241 L 63.162 55.095 L 59.462 55.095 L 59.462 57.725 L 63.162 57.725 L 63.162 58.579 L 59.462 58.579 L 59.462 61.21 L 63.162 61.21 L 63.162 62.064 L 58.35 62.064 L 58.35 54.24 L 63.162 54.24 Z M 72.711 57.949 C 72.076 57.764 71.566 57.501 71.181 57.16 C 70.796 56.82 70.603 56.408 70.603 55.926 C 70.603 55.456 70.803 55.041 71.203 54.684 C 71.602 54.327 72.098 54.149 72.693 54.149 C 73.278 54.149 73.78 54.332 74.198 54.698 C 74.416 54.886 74.586 55.151 74.708 55.492 L 73.713 55.834 C 73.59 55.54 73.479 55.35 73.381 55.264 C 73.195 55.088 72.949 54.99 72.693 54.991 C 72.441 54.99 72.198 55.084 72.013 55.255 C 71.818 55.423 71.709 55.669 71.715 55.926 C 71.715 56.195 71.818 56.423 72.023 56.611 C 72.236 56.793 72.527 56.959 72.895 57.111 C 73.6 57.314 74.19 57.626 74.665 58.046 C 75.21 58.525 75.483 59.09 75.483 59.742 C 75.483 60.395 75.213 60.959 74.675 61.436 C 74.135 61.912 73.475 62.151 72.693 62.151 C 71.906 62.151 71.24 61.906 70.695 61.417 C 70.269 61.043 70.007 60.56 69.909 59.965 L 70.978 59.623 C 71.043 60.192 71.226 60.607 71.525 60.868 C 71.853 61.158 72.242 61.302 72.693 61.302 C 73.147 61.302 73.543 61.153 73.882 60.854 C 74.22 60.555 74.389 60.184 74.389 59.742 C 74.389 59.3 74.223 58.932 73.891 58.639 C 73.543 58.339 73.143 58.105 72.711 57.949 Z M 84.933 55.095 L 84.933 62.064 L 83.827 62.064 L 83.827 55.094 L 81.713 55.094 L 81.713 54.241 L 87.041 54.241 L 87.041 55.095 Z"
        fill='var(--token-cdaba0c3-7192-4155-be78-0e3fe010491c, rgb(24, 24, 27)) /* {"name":"Gray 900 [Dynamic]"} */'
      ></path>
      <path
        d="M 72.693 54.506 C 72.176 54.506 71.767 54.659 71.44 54.951 C 71.105 55.251 70.96 55.571 70.96 55.926 C 70.96 56.297 71.103 56.614 71.417 56.891 C 71.753 57.189 72.212 57.431 72.811 57.604 L 72.711 57.948 L 72.835 57.612 C 73.323 57.792 73.755 58.044 74.125 58.369 L 74.127 58.37 C 74.536 58.731 74.747 59.197 74.747 59.742 C 74.747 60.289 74.531 60.757 74.118 61.122 C 73.727 61.474 73.219 61.666 72.693 61.66 C 72.176 61.668 71.675 61.481 71.29 61.136 C 70.993 60.876 70.803 60.517 70.698 60.088 L 70.33 60.206 C 70.442 60.584 70.643 60.895 70.93 61.146 L 70.934 61.15 C 71.408 61.575 71.987 61.792 72.693 61.792 C 73.397 61.792 73.972 61.58 74.439 61.167 C 74.91 60.75 75.126 60.28 75.126 59.742 C 75.126 59.205 74.908 58.734 74.431 58.315 L 74.43 58.315 C 74 57.935 73.46 57.645 72.797 57.455 L 72.778 57.45 L 72.76 57.442 C 72.37 57.282 72.043 57.097 71.792 56.884 L 71.787 56.879 L 71.782 56.875 C 71.511 56.635 71.356 56.289 71.359 55.926 C 71.359 55.554 71.5 55.232 71.778 54.986 C 72.038 54.756 72.348 54.633 72.693 54.633 C 73.043 54.633 73.356 54.759 73.619 54.996 C 73.724 55.089 73.816 55.227 73.901 55.391 L 74.22 55.281 C 74.153 55.164 74.066 55.058 73.965 54.968 L 73.964 54.968 C 73.615 54.661 73.198 54.506 72.693 54.506 Z M 72.599 58.288 C 71.933 58.093 71.377 57.81 70.945 57.428 C 70.489 57.025 70.247 56.518 70.247 55.926 C 70.247 55.338 70.501 54.831 70.965 54.416 C 71.437 53.995 72.022 53.791 72.693 53.791 C 73.36 53.791 73.946 54.001 74.433 54.427 C 74.706 54.665 74.906 54.986 75.044 55.369 L 75.168 55.712 L 73.513 56.281 L 73.384 55.972 C 73.26 55.677 73.176 55.558 73.145 55.532 L 73.143 55.529 C 73.022 55.413 72.861 55.348 72.693 55.349 C 72.528 55.348 72.37 55.41 72.25 55.523 C 72.132 55.623 72.067 55.772 72.073 55.926 C 72.073 56.087 72.128 56.22 72.259 56.342 C 72.431 56.487 72.679 56.632 73.014 56.772 C 73.753 56.988 74.385 57.321 74.901 57.777 C 75.513 58.315 75.841 58.974 75.841 59.742 C 75.841 60.508 75.518 61.167 74.911 61.704 C 74.301 62.244 73.554 62.508 72.693 62.508 C 71.826 62.508 71.075 62.236 70.459 61.684 C 69.968 61.254 69.668 60.694 69.557 60.024 L 69.507 59.718 L 71.283 59.149 L 71.333 59.581 C 71.393 60.101 71.552 60.416 71.759 60.597 L 71.761 60.599 C 72.021 60.829 72.326 60.944 72.693 60.944 C 73.062 60.944 73.374 60.826 73.646 60.585 C 73.909 60.352 74.032 60.079 74.032 59.742 C 74.032 59.403 73.912 59.133 73.656 58.908 C 73.344 58.639 72.986 58.429 72.599 58.288 Z M 44.977 53.883 L 48.142 53.883 C 48.796 53.883 49.367 54.08 49.828 54.489 C 50.284 54.892 50.533 55.389 50.533 55.969 C 50.533 56.385 50.409 56.758 50.173 57.081 C 50.266 57.151 50.367 57.236 50.475 57.333 C 51.119 57.903 51.455 58.61 51.455 59.438 C 51.455 60.263 51.105 60.975 50.444 61.559 C 49.902 62.042 49.224 62.324 48.43 62.419 L 48.409 62.421 L 44.977 62.421 Z M 50.237 57.6 L 50 57.867 C 49.894 57.77 49.782 57.68 49.665 57.597 C 49.624 57.566 49.578 57.54 49.53 57.521 L 48.833 57.371 L 49.37 56.901 C 49.681 56.629 49.82 56.324 49.82 55.969 C 49.82 55.622 49.68 55.311 49.356 55.026 C 49.04 54.746 48.644 54.599 48.142 54.599 L 45.69 54.599 L 45.69 61.705 L 48.366 61.705 C 49.029 61.623 49.557 61.392 49.97 61.024 L 49.971 61.023 C 50.499 60.556 50.741 60.033 50.741 59.438 C 50.741 58.83 50.505 58.313 50.001 57.868 L 50.238 57.6 Z M 57.994 53.883 L 63.52 53.883 L 63.52 55.453 L 59.82 55.453 L 59.82 57.367 L 63.52 57.367 L 63.52 58.937 L 59.82 58.937 L 59.82 60.852 L 63.52 60.852 L 63.52 62.421 L 57.994 62.421 Z M 58.707 54.599 L 58.707 61.705 L 62.806 61.705 L 62.806 61.568 L 59.106 61.568 L 59.106 58.22 L 62.806 58.22 L 62.806 58.082 L 59.106 58.082 L 59.106 54.735 L 62.806 54.735 L 62.806 54.6 L 58.707 54.6 Z M 81.357 53.883 L 87.398 53.883 L 87.398 55.453 L 85.29 55.453 L 85.29 62.421 L 83.47 62.421 L 83.47 55.452 L 81.356 55.452 L 81.356 53.883 Z M 82.07 54.599 L 82.07 54.736 L 84.184 54.736 L 84.184 61.706 L 84.577 61.706 L 84.577 54.736 L 86.685 54.736 L 86.685 54.6 L 82.07 54.6 Z M 46.089 54.731 L 48.136 54.731 C 48.466 54.731 48.761 54.85 49.011 55.071 C 49.277 55.306 49.415 55.614 49.415 55.969 C 49.415 56.322 49.288 56.632 49.024 56.863 C 48.818 57.049 48.549 57.137 48.26 57.163 L 48.244 57.165 L 46.09 57.165 L 46.09 54.73 Z M 46.803 55.447 L 46.803 56.449 L 48.211 56.449 C 48.394 56.43 48.493 56.379 48.547 56.329 L 48.551 56.327 L 48.554 56.323 C 48.648 56.243 48.701 56.137 48.701 55.969 C 48.706 55.83 48.646 55.696 48.539 55.607 C 48.43 55.504 48.286 55.447 48.136 55.447 Z M 46.089 57.302 L 48.056 57.302 C 48.656 57.302 49.187 57.504 49.636 57.903 C 50.093 58.31 50.33 58.829 50.33 59.438 C 50.33 60.046 50.093 60.564 49.635 60.97 C 49.202 61.361 48.639 61.574 48.056 61.568 L 46.089 61.568 Z M 46.803 58.018 L 46.803 60.852 L 48.056 60.852 C 48.482 60.852 48.846 60.714 49.163 60.432 C 49.471 60.161 49.617 59.836 49.617 59.437 C 49.617 59.038 49.471 58.713 49.162 58.439 C 48.861 58.163 48.465 58.012 48.056 58.018 Z M 83.613 42.022 L 81.912 42.022 L 80.98 39.089 L 77.742 39.089 L 76.855 42.022 L 75.199 42.022 L 78.407 32.037 L 80.389 32.037 Z M 80.7 37.86 L 79.857 35.253 C 79.768 34.986 79.601 34.359 79.355 33.372 L 79.325 33.372 C 79.175 34.001 79.017 34.627 78.851 35.252 L 78.024 37.86 Z M 90.786 38.334 C 90.786 39.558 90.456 40.526 89.796 41.237 C 89.204 41.869 88.469 42.185 87.592 42.185 C 86.646 42.185 85.966 41.845 85.552 41.163 L 85.522 41.163 L 85.522 44.956 L 83.925 44.956 L 83.925 37.193 C 83.925 36.423 83.905 35.633 83.866 34.823 L 85.27 34.823 L 85.36 35.963 L 85.39 35.963 C 85.922 35.104 86.73 34.675 87.814 34.675 C 88.662 34.675 89.37 35.01 89.936 35.682 C 90.503 36.354 90.786 37.238 90.786 38.334 Z M 89.16 38.392 C 89.16 37.692 89.003 37.114 88.687 36.659 C 88.341 36.185 87.877 35.948 87.297 35.948 C 86.903 35.948 86.545 36.08 86.225 36.34 C 85.905 36.602 85.683 36.965 85.597 37.37 C 85.547 37.569 85.523 37.73 85.523 37.858 L 85.523 39.058 C 85.523 39.582 85.683 40.024 86.003 40.385 C 86.324 40.746 86.74 40.926 87.253 40.926 C 87.853 40.926 88.322 40.693 88.657 40.23 C 88.992 39.765 89.16 39.153 89.16 38.392 Z M 98.244 38.334 C 98.244 39.558 97.914 40.526 97.252 41.237 C 96.662 41.869 95.927 42.185 95.05 42.185 C 94.103 42.185 93.423 41.845 93.01 41.163 L 92.98 41.163 L 92.98 44.956 L 91.383 44.956 L 91.383 37.193 C 91.383 36.423 91.363 35.633 91.324 34.823 L 92.728 34.823 L 92.818 35.963 L 92.848 35.963 C 93.379 35.104 94.188 34.675 95.272 34.675 C 96.119 34.675 96.827 35.01 97.394 35.682 C 97.96 36.354 98.244 37.238 98.244 38.334 Z M 96.617 38.392 C 96.617 37.692 96.459 37.114 96.143 36.659 C 95.798 36.185 95.336 35.948 94.754 35.948 C 94.362 35.948 93.982 36.087 93.682 36.34 C 93.362 36.602 93.14 36.965 93.054 37.37 C 93.005 37.569 92.98 37.73 92.98 37.858 L 92.98 39.058 C 92.98 39.582 93.14 40.024 93.46 40.385 C 93.78 40.745 94.196 40.926 94.71 40.926 C 95.311 40.926 95.78 40.693 96.114 40.23 C 96.449 39.765 96.617 39.153 96.617 38.392 Z M 106.68 39.222 C 106.68 40.072 106.385 40.762 105.795 41.296 C 105.145 41.879 104.241 42.17 103.08 42.17 C 102.008 42.17 101.148 41.963 100.497 41.548 L 100.867 40.214 C 101.562 40.637 102.361 40.858 103.175 40.852 C 103.776 40.852 104.244 40.716 104.581 40.444 C 104.915 40.172 105.082 39.808 105.082 39.354 C 105.091 38.97 104.942 38.599 104.668 38.33 C 104.393 38.053 103.934 37.796 103.293 37.558 C 101.548 36.906 100.677 35.951 100.677 34.695 C 100.677 33.874 100.982 33.201 101.594 32.677 C 102.204 32.153 103.017 31.891 104.034 31.891 C 104.941 31.891 105.694 32.049 106.295 32.365 L 105.896 33.669 C 105.335 33.363 104.7 33.209 103.989 33.209 C 103.427 33.209 102.989 33.349 102.674 33.625 C 102.416 33.854 102.27 34.184 102.275 34.529 C 102.275 34.923 102.427 35.249 102.732 35.506 C 102.998 35.744 103.481 36 104.182 36.277 C 105.04 36.623 105.669 37.027 106.075 37.491 C 106.478 37.953 106.68 38.531 106.68 39.221 Z M 111.151 36.023 L 109.391 36.023 L 109.391 39.518 C 109.391 40.408 109.701 40.852 110.323 40.852 C 110.608 40.852 110.844 40.827 111.032 40.777 L 111.076 41.992 C 110.761 42.11 110.347 42.169 109.834 42.169 C 109.204 42.169 108.711 41.976 108.355 41.591 C 108.001 41.206 107.823 40.559 107.823 39.651 L 107.823 36.021 L 106.774 36.021 L 106.774 34.821 L 107.823 34.821 L 107.823 33.502 L 109.391 33.028 L 109.391 34.821 L 111.151 34.821 Z M 118.283 38.363 C 118.283 39.469 117.967 40.378 117.336 41.089 C 116.675 41.82 115.797 42.185 114.703 42.185 C 113.649 42.185 112.809 41.835 112.183 41.133 C 111.557 40.433 111.244 39.548 111.244 38.482 C 111.244 37.366 111.566 36.452 112.212 35.742 C 112.857 35.031 113.727 34.675 114.821 34.675 C 115.876 34.675 116.724 35.025 117.365 35.727 C 117.977 36.407 118.283 37.286 118.283 38.363 Z M 116.626 38.414 C 116.626 37.75 116.485 37.181 116.198 36.706 C 115.863 36.131 115.385 35.845 114.765 35.845 C 114.123 35.845 113.635 36.132 113.301 36.706 C 113.014 37.182 112.872 37.76 112.872 38.444 C 112.872 39.108 113.014 39.678 113.301 40.152 C 113.646 40.727 114.128 41.013 114.75 41.013 C 115.36 41.013 115.838 40.721 116.184 40.137 C 116.478 39.653 116.626 39.077 116.626 38.414 Z M 122.665 36.23 C 122.499 36.2 122.331 36.185 122.162 36.185 C 121.6 36.185 121.166 36.397 120.86 36.823 C 120.594 37.198 120.461 37.673 120.461 38.245 L 120.461 42.022 L 118.865 42.022 L 118.88 37.09 C 118.88 36.26 118.86 35.505 118.82 34.823 L 120.211 34.823 L 120.269 36.201 L 120.314 36.201 C 120.464 35.752 120.741 35.356 121.112 35.061 C 121.445 34.811 121.85 34.676 122.266 34.675 C 122.413 34.675 122.547 34.685 122.665 34.705 L 122.665 36.229 Z M 129.269 38.081 C 129.269 38.368 129.25 38.61 129.211 38.807 L 124.421 38.807 C 124.44 39.518 124.671 40.062 125.116 40.437 C 125.52 40.773 126.042 40.941 126.683 40.941 C 127.392 40.941 128.039 40.827 128.621 40.601 L 128.871 41.711 C 128.191 42.008 127.388 42.156 126.462 42.156 C 125.347 42.156 124.473 41.826 123.836 41.17 C 123.201 40.514 122.883 39.632 122.883 38.526 C 122.883 37.441 123.179 36.536 123.771 35.816 C 124.391 35.046 125.229 34.661 126.283 34.661 C 127.319 34.661 128.103 35.046 128.636 35.816 C 129.057 36.427 129.269 37.183 129.269 38.081 Z M 127.747 37.666 C 127.757 37.192 127.653 36.783 127.437 36.436 C 127.16 35.992 126.736 35.77 126.164 35.77 C 125.642 35.77 125.218 35.987 124.894 36.422 C 124.622 36.784 124.458 37.215 124.421 37.666 Z"
        fill="currentColor"
      ></path>
    </g>
  </svg>
);

const FeatureIcon2 = () => (
  <svg viewBox="0 0 202 108" className="h-56 w-56 sm:h-72 sm:w-72">
    <path
      d="M 23.552 44.487 C 23.552 44.487 20.81 42.306 20.89 38.602 C 20.968 34.896 24.176 32.848 24.176 32.848 C 24.176 32.848 26.502 35.198 26.426 39.005 C 26.341 42.807 23.552 44.487 23.552 44.487 Z M 34.407 46.992 C 31.419 49.186 28.065 48.192 28.065 48.192 C 28.065 48.192 27.838 44.947 30.896 42.693 C 33.963 40.445 37.219 40.997 37.219 40.997 C 37.219 40.997 37.389 44.803 34.407 46.992 Z M 32.952 49.655 C 32.952 49.655 33.614 53.407 30.949 55.974 C 28.277 58.533 24.813 57.991 24.813 57.991 C 24.813 57.991 24.167 54.795 26.908 52.164 C 29.651 49.537 32.952 49.655 32.952 49.655 Z M 31.856 64.076 C 29.436 66.873 25.944 66.656 25.944 66.656 C 25.944 66.656 24.998 63.546 27.484 60.661 C 29.972 57.784 33.266 57.595 33.266 57.595 C 33.266 57.595 34.273 61.268 31.856 64.075 Z M 33.759 71.894 C 32.244 75.277 28.835 76.068 28.835 76.068 C 28.835 76.068 27.036 73.355 28.585 69.886 C 30.141 66.414 33.252 65.283 33.252 65.283 C 33.252 65.283 35.27 68.507 33.759 71.893 Z M 30.184 39.661 C 30.184 39.661 29.113 36.329 31.228 33.291 C 33.348 30.249 37.156 30.327 37.156 30.327 C 37.156 30.327 37.785 33.579 35.61 36.692 C 33.436 39.815 30.184 39.662 30.184 39.662 Z M 21.857 45.726 C 22.898 49.377 20.728 51.811 20.728 51.811 C 20.728 51.811 17.469 50.539 16.451 46.974 C 15.434 43.411 17.899 40.506 17.899 40.506 C 17.899 40.506 20.812 42.068 21.857 45.726 Z M 19.968 54.062 C 21.792 57.392 20.218 60.246 20.218 60.246 C 20.218 60.246 16.756 59.73 14.973 56.48 C 13.193 53.233 14.947 49.85 14.947 49.85 C 14.947 49.85 18.141 50.724 19.968 54.062 Z M 19.355 62.042 C 22.102 64.662 21.462 67.859 21.462 67.859 C 21.462 67.859 18.007 68.42 15.327 65.859 C 12.65 63.306 13.3 59.549 13.3 59.549 C 13.3 59.549 16.598 59.416 19.355 62.042 Z M 22.057 71.046 C 25.097 73.324 24.843 76.564 24.843 76.564 C 24.843 76.564 21.482 77.537 18.515 75.322 C 15.547 73.102 15.754 69.296 15.754 69.296 C 15.754 69.296 19.009 68.775 22.057 71.046 Z"
      fill="currentColor"
    ></path>
    <path
      d="M 28.52 40.519 L 29.752 41.855 C 29.199 42.363 16.476 54.675 30.457 80.61 L 28.865 81.47 C 14.175 54.237 28.375 40.657 28.52 40.519 Z M 177.829 44.487 C 177.829 44.487 180.571 42.306 180.491 38.601 C 180.413 34.895 177.205 32.848 177.205 32.848 C 177.205 32.848 174.879 35.198 174.955 39.005 C 175.04 42.806 177.829 44.487 177.829 44.487 Z M 166.974 46.992 C 169.962 49.186 173.316 48.192 173.316 48.192 C 173.316 48.192 173.543 44.947 170.485 42.693 C 167.418 40.444 164.162 40.996 164.162 40.996 C 164.162 40.996 163.992 44.803 166.974 46.992 Z M 168.429 49.655 C 168.429 49.655 167.767 53.407 170.432 55.974 C 173.104 58.533 176.568 57.991 176.568 57.991 C 176.568 57.991 177.214 54.795 174.473 52.164 C 171.73 49.537 168.429 49.655 168.429 49.655 Z M 169.525 64.076 C 171.945 66.873 175.437 66.656 175.437 66.656 C 175.437 66.656 176.383 63.546 173.898 60.661 C 171.409 57.784 168.114 57.595 168.114 57.595 C 168.114 57.595 167.108 61.268 169.525 64.075 Z M 167.622 71.894 C 169.136 75.277 172.545 76.068 172.545 76.068 C 172.545 76.068 174.345 73.355 172.797 69.886 C 171.24 66.414 168.129 65.283 168.129 65.283 C 168.129 65.283 166.111 68.507 167.622 71.893 Z M 171.197 39.661 C 171.197 39.661 172.268 36.329 170.153 33.291 C 168.032 30.249 164.225 30.327 164.225 30.327 C 164.225 30.327 163.596 33.579 165.771 36.692 C 167.945 39.815 171.197 39.662 171.197 39.662 Z M 179.525 45.726 C 178.483 49.377 180.653 51.811 180.653 51.811 C 180.653 51.811 183.912 50.539 184.93 46.974 C 185.947 43.411 183.483 40.506 183.483 40.506 C 183.483 40.506 180.569 42.068 179.525 45.726 Z M 181.413 54.062 C 179.589 57.392 181.163 60.246 181.163 60.246 C 181.163 60.246 184.625 59.73 186.408 56.48 C 188.188 53.233 186.434 49.85 186.434 49.85 C 186.434 49.85 183.241 50.724 181.413 54.062 Z M 182.026 62.042 C 179.279 64.662 179.919 67.859 179.919 67.859 C 179.919 67.859 183.374 68.42 186.054 65.859 C 188.731 63.306 188.081 59.549 188.081 59.549 C 188.081 59.549 184.783 59.416 182.026 62.042 Z M 179.324 71.046 C 176.283 73.324 176.538 76.564 176.538 76.564 C 176.538 76.564 179.899 77.537 182.865 75.322 C 185.833 73.102 185.626 69.296 185.626 69.296 C 185.626 69.296 182.371 68.775 179.324 71.046 Z"
      fill="currentColor"
    ></path>
    <path
      d="M 172.86 40.519 L 171.628 41.855 C 172.182 42.363 184.905 54.675 170.924 80.61 L 172.515 81.47 C 187.206 54.237 173.005 40.657 172.86 40.519 Z"
      fill="currentColor"
    ></path>
    <path
      d="M 50.182 54.736 L 45.585 54.736 L 45.585 51.583 L 50.453 51.583 L 50.453 49.753 L 43.36 49.753 L 43.36 61.965 L 50.726 61.965 L 50.726 60.135 L 45.586 60.135 L 45.586 56.548 L 50.183 56.548 L 50.183 54.736 Z M 58.488 49.1 L 58.488 54.101 L 58.452 54.101 C 58.054 53.449 57.185 52.941 55.973 52.941 C 53.856 52.941 52.01 54.699 52.028 57.653 C 52.028 60.371 53.693 62.165 55.792 62.165 C 57.077 62.165 58.145 61.549 58.669 60.57 L 58.706 60.57 L 58.796 61.965 L 60.786 61.965 C 60.739 61.138 60.715 60.311 60.714 59.483 L 60.714 49.1 L 58.49 49.1 Z M 58.488 58.142 C 58.488 58.378 58.47 58.595 58.415 58.794 C 58.198 59.754 57.402 60.371 56.497 60.371 C 55.086 60.371 54.289 59.193 54.289 57.581 C 54.289 55.931 55.086 54.663 56.515 54.663 C 57.529 54.663 58.235 55.37 58.434 56.24 C 58.47 56.42 58.488 56.638 58.488 56.82 Z M 65.347 61.965 L 65.347 53.141 L 63.103 53.141 L 63.103 61.965 Z M 64.225 49.463 C 63.483 49.463 62.976 49.988 62.976 50.677 C 62.976 51.347 63.465 51.873 64.206 51.873 C 64.985 51.873 65.474 51.347 65.474 50.677 C 65.455 49.988 64.985 49.463 64.224 49.463 Z M 68.115 51.148 L 68.115 53.141 L 66.85 53.141 L 66.85 54.808 L 68.117 54.808 L 68.117 58.958 C 68.117 60.118 68.334 60.914 68.804 61.422 C 69.22 61.875 69.908 62.147 70.723 62.147 C 71.428 62.147 72.007 62.056 72.333 61.929 L 72.297 60.226 C 72.097 60.28 71.808 60.334 71.428 60.334 C 70.578 60.334 70.288 59.773 70.288 58.704 L 70.288 54.808 L 72.405 54.808 L 72.405 53.141 L 70.288 53.141 L 70.288 50.622 L 68.117 51.148 Z M 78.087 52.942 C 75.427 52.942 73.527 54.718 73.527 57.617 C 73.527 60.443 75.445 62.165 77.942 62.165 C 80.186 62.165 82.467 60.715 82.467 57.472 C 82.467 54.79 80.711 52.942 78.087 52.942 Z M 78.032 54.572 C 79.552 54.572 80.168 56.149 80.168 57.526 C 80.168 59.302 79.281 60.552 78.014 60.552 C 76.657 60.552 75.824 59.266 75.824 57.562 C 75.824 56.095 76.458 54.572 78.032 54.572 Z M 84.294 61.965 L 86.52 61.965 L 86.52 57.4 C 86.52 57.146 86.556 56.911 86.592 56.712 C 86.792 55.715 87.515 55.027 88.583 55.027 C 88.855 55.027 89.053 55.063 89.234 55.099 L 89.234 52.997 C 89.062 52.959 88.886 52.941 88.71 52.943 C 87.769 52.943 86.737 53.577 86.303 54.827 L 86.231 54.827 L 86.158 53.142 L 84.222 53.142 C 84.276 53.939 84.294 54.791 84.294 55.987 L 84.294 61.967 Z M 90.194 61.531 C 90.845 61.893 91.804 62.147 92.872 62.147 C 95.207 62.147 96.455 60.987 96.455 59.392 C 96.437 58.088 95.695 57.272 94.103 56.692 C 92.981 56.294 92.565 56.022 92.565 55.46 C 92.565 54.917 92.999 54.536 93.795 54.536 C 94.573 54.536 95.279 54.826 95.659 55.044 L 96.093 53.467 C 95.587 53.195 94.736 52.942 93.759 52.942 C 91.714 52.942 90.429 54.156 90.429 55.714 C 90.411 56.747 91.135 57.689 92.854 58.287 C 93.94 58.667 94.302 58.976 94.302 59.574 C 94.302 60.154 93.867 60.552 92.89 60.552 C 92.094 60.552 91.135 60.226 90.628 59.918 Z M 98.79 49.717 C 98.573 51.184 98.102 52.997 97.632 54.319 L 98.989 54.192 C 99.677 53.069 100.455 51.202 100.943 49.553 Z M 114.769 59.845 C 114.19 60.117 113.249 60.298 112.344 60.298 C 109.666 60.298 108.091 58.577 108.091 55.895 C 108.091 52.96 109.883 51.402 112.362 51.402 C 113.376 51.402 114.172 51.619 114.751 51.872 L 115.24 50.097 C 114.787 49.861 113.738 49.553 112.272 49.553 C 108.544 49.553 105.757 51.981 105.757 56.004 C 105.757 59.719 108.109 62.147 111.982 62.147 C 113.448 62.147 114.606 61.875 115.131 61.603 Z M 116.94 61.965 L 119.184 61.965 L 119.184 56.692 C 119.184 56.439 119.202 56.203 119.275 56.022 C 119.51 55.352 120.125 54.772 120.994 54.772 C 122.225 54.772 122.695 55.732 122.695 57.019 L 122.695 61.965 L 124.921 61.965 L 124.921 56.765 C 124.921 53.938 123.347 52.942 121.845 52.942 C 121.284 52.942 120.759 53.087 120.324 53.34 C 119.854 53.594 119.492 53.938 119.221 54.355 L 119.184 54.355 L 119.184 49.1 L 116.94 49.1 Z M 131.291 52.942 C 128.631 52.942 126.73 54.718 126.73 57.617 C 126.73 60.443 128.649 62.165 131.146 62.165 C 133.39 62.165 135.67 60.715 135.67 57.472 C 135.67 54.79 133.915 52.942 131.291 52.942 Z M 131.237 54.572 C 132.757 54.572 133.372 56.149 133.372 57.526 C 133.372 59.302 132.485 60.552 131.219 60.552 C 129.861 60.552 129.029 59.266 129.029 57.562 C 129.029 56.095 129.662 54.572 131.237 54.572 Z M 139.742 61.965 L 139.742 53.141 L 137.498 53.141 L 137.498 61.965 Z M 138.62 49.463 C 137.878 49.463 137.372 49.988 137.372 50.677 C 137.372 51.347 137.86 51.873 138.602 51.873 C 139.38 51.873 139.869 51.347 139.869 50.677 C 139.851 49.988 139.38 49.463 138.62 49.463 Z M 148.302 60.045 C 147.747 60.28 147.149 60.397 146.546 60.389 C 145.026 60.389 143.85 59.356 143.85 57.544 C 143.832 55.932 144.845 54.681 146.546 54.681 C 147.343 54.681 147.867 54.862 148.229 55.026 L 148.628 53.358 C 148.121 53.123 147.288 52.942 146.474 52.942 C 143.38 52.942 141.57 55.007 141.57 57.652 C 141.57 60.389 143.361 62.147 146.112 62.147 C 147.216 62.147 148.139 61.911 148.609 61.694 L 148.302 60.044 Z M 157.73 58.178 C 157.766 57.979 157.802 57.653 157.802 57.236 C 157.802 55.297 156.861 52.942 154.002 52.942 C 151.179 52.942 149.695 55.243 149.695 57.689 C 149.695 60.389 151.378 62.147 154.237 62.147 C 155.504 62.147 156.554 61.911 157.314 61.603 L 156.988 60.063 C 156.319 60.316 155.577 60.461 154.545 60.461 C 153.134 60.461 151.885 59.773 151.831 58.178 Z M 151.831 56.62 C 151.921 55.714 152.5 54.464 153.876 54.464 C 155.36 54.464 155.722 55.804 155.703 56.62 Z"
      fill="currentColor"
    ></path>
  </svg>
);

const FeatureIcon3 = () => (
  <svg viewBox="0 0 240 108" className="h-56 w-56 sm:h-72 sm:w-72">
    <g>
      <path d="M 0 0 L 240 0 L 240 108 L 0 108 Z" fill="transparent"></path>
      <path
        d="M 21.273 41.24 C 21.273 41.24 17.863 38.525 17.96 33.917 C 18.055 29.306 22.042 26.76 22.042 26.76 C 22.042 26.76 24.935 29.686 24.842 34.422 C 24.739 39.152 21.273 41.24 21.273 41.24 Z M 34.768 44.362 C 31.055 47.09 26.884 45.852 26.884 45.852 C 26.884 45.852 26.601 41.815 30.401 39.012 C 34.213 36.216 38.261 36.904 38.261 36.904 C 38.261 36.904 38.474 41.641 34.768 44.362 Z M 32.961 47.675 C 32.961 47.675 33.786 52.343 30.474 55.535 C 27.154 58.718 22.848 58.042 22.848 58.042 C 22.848 58.042 22.043 54.065 25.448 50.792 C 28.858 47.527 32.961 47.675 32.961 47.675 Z M 31.605 65.615 C 28.598 69.095 24.257 68.823 24.257 68.823 C 24.257 68.823 23.079 64.952 26.167 61.365 C 29.26 57.787 33.356 57.553 33.356 57.553 C 33.356 57.553 34.608 62.123 31.606 65.615 Z M 33.975 75.344 C 32.094 79.552 27.856 80.534 27.856 80.534 C 27.856 80.534 25.618 77.159 27.541 72.843 C 29.474 68.524 33.341 67.118 33.341 67.118 C 33.341 67.118 35.851 71.131 33.975 75.344 Z M 29.514 35.24 C 29.514 35.24 28.181 31.094 30.809 27.314 C 33.444 23.531 38.177 23.63 38.177 23.63 C 38.177 23.63 38.96 27.676 36.257 31.549 C 33.557 35.432 29.514 35.239 29.514 35.239 Z M 19.166 42.78 C 20.462 47.324 17.766 50.351 17.766 50.351 C 17.766 50.351 13.714 48.767 12.446 44.331 C 11.181 39.898 14.243 36.284 14.243 36.284 C 14.243 36.284 17.865 38.229 19.166 42.781 Z M 16.823 53.15 C 19.092 57.294 17.136 60.844 17.136 60.844 C 17.136 60.844 12.832 60.202 10.614 56.157 C 8.399 52.116 10.578 47.907 10.578 47.907 C 10.578 47.907 14.549 48.997 16.823 53.15 Z M 16.063 63.08 C 19.48 66.34 18.686 70.317 18.686 70.317 C 18.686 70.317 14.392 71.014 11.058 67.827 C 7.728 64.649 8.536 59.974 8.536 59.974 C 8.536 59.974 12.636 59.811 16.063 63.079 Z M 19.428 74.282 C 23.208 77.118 22.894 81.15 22.894 81.15 C 22.894 81.15 18.716 82.358 15.027 79.601 C 11.337 76.838 11.592 72.101 11.592 72.101 C 11.592 72.101 15.638 71.456 19.428 74.282 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 27.447 36.306 L 28.979 37.969 C 28.291 38.601 12.479 53.913 29.874 86.186 L 27.896 87.255 C 9.62 53.365 27.266 36.477 27.446 36.306 Z M 218.727 41.24 C 218.727 41.24 222.137 38.526 222.04 33.917 C 221.945 29.307 217.958 26.76 217.958 26.76 C 217.958 26.76 215.065 29.686 215.157 34.422 C 215.261 39.152 218.727 41.241 218.727 41.241 Z M 205.232 44.362 C 208.945 47.09 213.116 45.853 213.116 45.853 C 213.116 45.853 213.399 41.815 209.599 39.013 C 205.787 36.216 201.74 36.905 201.74 36.905 C 201.74 36.905 201.526 41.641 205.232 44.362 Z M 207.039 47.675 C 207.039 47.675 206.214 52.344 209.526 55.535 C 212.846 58.718 217.152 58.042 217.152 58.042 C 217.152 58.042 217.958 54.066 214.551 50.793 C 211.143 47.527 207.039 47.675 207.039 47.675 Z M 208.395 65.616 C 211.402 69.095 215.743 68.823 215.743 68.823 C 215.743 68.823 216.921 64.953 213.832 61.365 C 210.74 57.787 206.644 57.554 206.644 57.554 C 206.644 57.554 205.392 62.124 208.395 65.616 Z M 206.025 75.344 C 207.906 79.552 212.143 80.534 212.143 80.534 C 212.143 80.534 214.382 77.159 212.459 72.843 C 210.526 68.525 206.659 67.118 206.659 67.118 C 206.659 67.118 204.149 71.131 206.025 75.344 Z M 210.486 35.24 C 210.486 35.24 211.819 31.094 209.191 27.314 C 206.556 23.532 201.823 23.63 201.823 23.63 C 201.823 23.63 201.04 27.677 203.742 31.549 C 206.443 35.432 210.486 35.24 210.486 35.24 Z M 220.834 42.781 C 219.538 47.324 222.234 50.351 222.234 50.351 C 222.234 50.351 226.286 48.767 227.554 44.331 C 228.819 39.898 225.757 36.285 225.757 36.285 C 225.757 36.285 222.135 38.229 220.834 42.781 Z M 223.178 53.15 C 220.908 57.295 222.864 60.844 222.864 60.844 C 222.864 60.844 227.168 60.202 229.386 56.157 C 231.601 52.117 229.422 47.907 229.422 47.907 C 229.422 47.907 225.451 48.997 223.178 53.151 Z M 223.937 63.08 C 220.52 66.341 221.314 70.317 221.314 70.317 C 221.314 70.317 225.608 71.014 228.942 67.827 C 232.271 64.649 231.464 59.975 231.464 59.975 C 231.464 59.975 227.365 59.811 223.937 63.08 Z M 220.572 74.283 C 216.792 77.119 217.106 81.15 217.106 81.15 C 217.106 81.15 221.284 82.358 224.973 79.601 C 228.663 76.838 228.409 72.102 228.409 72.102 C 228.409 72.102 224.362 71.456 220.572 74.282 Z"
        fill="currentColor"
      ></path>
      <path
        d="M 212.554 36.306 L 211.021 37.969 C 211.709 38.601 227.52 53.913 210.126 86.187 L 212.104 87.255 C 230.38 53.365 212.733 36.478 212.554 36.306 Z M 88.95 47.412 L 90.882 47.412 L 90.882 37.985 L 88.951 37.985 L 88.951 47.412 Z M 89.917 36.605 C 90.543 36.605 91.043 36.112 91.043 35.503 C 91.043 34.884 90.543 34.391 89.917 34.391 C 89.3 34.391 88.799 34.884 88.799 35.503 C 88.799 36.112 89.299 36.605 89.917 36.605 Z M 93.091 34.481 L 93.091 47.412 L 95.094 47.412 L 95.094 42.994 L 97.867 42.994 C 100.397 42.994 102.177 41.256 102.177 38.729 C 102.177 36.211 100.433 34.481 97.929 34.481 Z M 95.094 36.175 L 97.402 36.175 C 99.136 36.175 100.129 37.107 100.129 38.738 C 100.129 40.368 99.136 41.31 97.393 41.31 L 95.094 41.31 Z M 106.237 46.041 C 105.2 46.041 104.52 45.503 104.52 44.679 C 104.52 43.872 105.173 43.362 106.326 43.281 L 108.643 43.138 L 108.643 43.881 C 108.643 45.109 107.587 46.041 106.237 46.041 Z M 105.7 47.573 C 106.935 47.573 108.124 46.91 108.669 45.862 L 108.714 45.862 L 108.714 47.412 L 110.565 47.412 L 110.565 40.915 C 110.565 39.025 109.108 37.815 106.827 37.815 C 104.502 37.815 103.053 39.069 102.946 40.772 L 104.77 40.772 C 104.94 39.929 105.638 39.392 106.756 39.392 C 107.936 39.392 108.643 40.019 108.643 41.067 L 108.643 41.793 L 106.067 41.946 C 103.831 42.071 102.571 43.084 102.571 44.733 C 102.571 46.426 103.858 47.573 105.7 47.573 Z M 115.976 47.573 C 117.344 47.573 118.417 46.883 118.971 45.808 L 119.007 45.808 L 119.007 47.412 L 120.885 47.412 L 120.885 34.481 L 118.945 34.481 L 118.945 39.553 L 118.909 39.553 C 118.381 38.513 117.308 37.833 115.958 37.833 C 113.597 37.833 112.023 39.723 112.023 42.699 C 112.023 45.674 113.597 47.573 115.976 47.573 Z M 116.485 39.463 C 117.97 39.463 118.963 40.745 118.963 42.699 C 118.963 44.67 117.97 45.934 116.485 45.934 C 114.956 45.934 113.999 44.688 113.999 42.699 C 113.999 40.718 114.956 39.464 116.485 39.464 Z M 126.823 34.481 L 126.823 47.412 L 128.826 47.412 L 128.826 42.994 L 131.599 42.994 C 134.129 42.994 135.909 41.256 135.909 38.729 C 135.909 36.211 134.165 34.481 131.661 34.481 Z M 128.826 36.175 L 131.134 36.175 C 132.868 36.175 133.861 37.107 133.861 38.738 C 133.861 40.368 132.868 41.31 131.125 41.31 L 128.826 41.31 Z M 137.429 47.412 L 139.361 47.412 L 139.361 41.767 C 139.361 40.431 140.13 39.597 141.373 39.597 C 141.749 39.597 142.088 39.643 142.24 39.705 L 142.24 37.905 C 142.022 37.86 141.801 37.836 141.579 37.833 C 140.47 37.833 139.629 38.487 139.316 39.589 L 139.271 39.589 L 139.271 37.985 L 137.429 37.985 Z M 147.069 47.591 C 149.761 47.591 151.532 45.736 151.532 42.699 C 151.532 39.669 149.752 37.815 147.069 37.815 C 144.387 37.815 142.607 39.67 142.607 42.699 C 142.607 45.736 144.378 47.591 147.069 47.591 Z M 147.069 45.996 C 145.558 45.996 144.574 44.796 144.574 42.699 C 144.574 40.611 145.558 39.409 147.069 39.409 C 148.581 39.409 149.564 40.611 149.564 42.699 C 149.564 44.795 148.59 45.996 147.069 45.996 Z M 59.699 72.739 L 62.52 72.739 L 57.209 57.739 L 54.19 57.739 L 48.879 72.739 L 51.639 72.739 L 52.914 68.862 L 58.423 68.862 Z M 55.632 60.389 L 55.705 60.389 L 57.79 66.793 L 53.537 66.793 Z M 69.906 61.533 C 68.308 61.533 67.064 62.343 66.441 63.623 L 66.389 63.623 L 66.389 61.72 L 63.869 61.72 L 63.869 76.367 L 66.452 76.367 L 66.452 70.909 L 66.504 70.909 C 67.105 72.146 68.35 72.916 69.948 72.916 C 72.707 72.916 74.47 70.743 74.47 67.23 C 74.47 63.706 72.697 61.533 69.906 61.533 Z M 69.118 70.795 C 67.51 70.795 66.441 69.392 66.431 67.229 C 66.441 65.089 67.51 63.664 69.118 63.664 C 70.788 63.664 71.825 65.057 71.825 67.23 C 71.825 69.412 70.788 70.795 69.118 70.795 Z M 82.168 61.533 C 80.57 61.533 79.325 62.343 78.703 63.623 L 78.651 63.623 L 78.651 61.72 L 76.131 61.72 L 76.131 76.367 L 78.713 76.367 L 78.713 70.909 L 78.765 70.909 C 79.367 72.146 80.612 72.916 82.209 72.916 C 84.969 72.916 86.732 70.743 86.732 67.23 C 86.732 63.706 84.958 61.533 82.168 61.533 Z M 81.379 70.795 C 79.771 70.795 78.703 69.392 78.692 67.229 C 78.702 65.089 79.772 63.664 81.379 63.664 C 83.049 63.664 84.087 65.057 84.087 67.23 C 84.087 69.412 83.049 70.795 81.379 70.795 Z M 97.51 72.957 C 100.736 72.957 102.832 70.805 102.832 67.23 C 102.832 63.664 100.715 61.491 97.51 61.491 C 94.305 61.491 92.188 63.674 92.188 67.23 C 92.188 70.805 94.284 72.957 97.51 72.957 Z M 97.51 70.909 C 95.871 70.909 94.813 69.579 94.813 67.229 C 94.813 64.891 95.881 63.549 97.51 63.549 C 99.15 63.549 100.207 64.891 100.207 67.229 C 100.207 69.579 99.149 70.909 97.51 70.909 Z M 105.207 72.739 L 107.79 72.739 L 107.79 63.716 L 109.948 63.716 L 109.948 61.751 L 107.749 61.751 L 107.749 60.847 C 107.749 59.953 108.195 59.485 109.18 59.485 C 109.481 59.485 109.782 59.516 110 59.548 L 110 57.738 C 109.541 57.661 109.076 57.623 108.61 57.624 C 106.224 57.624 105.207 58.633 105.207 60.774 L 105.207 61.751 L 103.703 61.751 L 103.703 63.716 L 105.207 63.716 Z M 116.618 59.142 L 116.618 61.72 L 115.041 61.72 L 115.041 63.716 L 116.618 63.716 L 116.618 69.808 C 116.618 71.948 117.552 72.791 119.927 72.791 C 120.477 72.791 120.985 72.749 121.265 72.687 L 121.265 70.712 C 121.021 70.747 120.775 70.765 120.529 70.764 C 119.616 70.764 119.201 70.348 119.201 69.444 L 119.201 63.716 L 121.276 63.716 L 121.276 61.72 L 119.201 61.72 L 119.201 59.142 Z M 123.112 72.739 L 125.695 72.739 L 125.695 66.409 C 125.695 64.787 126.639 63.706 128.195 63.706 C 129.689 63.706 130.456 64.631 130.456 66.169 L 130.456 72.739 L 133.039 72.739 L 133.039 65.639 C 133.039 63.113 131.618 61.512 129.191 61.512 C 127.521 61.512 126.307 62.271 125.716 63.612 L 125.664 63.612 L 125.664 57.739 L 123.112 57.739 Z M 139.855 63.498 C 141.338 63.498 142.334 64.589 142.386 66.148 L 137.251 66.148 C 137.355 64.61 138.403 63.498 139.855 63.498 Z M 142.396 69.444 C 142.116 70.358 141.203 70.961 140 70.961 C 138.309 70.961 137.23 69.776 137.23 68.009 L 137.23 67.853 L 144.917 67.853 L 144.917 67.011 C 144.917 63.664 142.967 61.491 139.844 61.491 C 136.67 61.491 134.658 63.789 134.658 67.271 C 134.658 70.785 136.649 72.957 139.948 72.957 C 142.552 72.957 144.481 71.523 144.813 69.444 Z M 157.977 72.739 L 157.977 66.824 L 163.226 57.739 L 160.311 57.739 L 156.691 64.267 L 156.629 64.267 L 152.998 57.739 L 150.062 57.739 L 155.311 66.824 L 155.311 72.739 Z M 167.137 63.498 C 168.621 63.498 169.616 64.589 169.668 66.148 L 164.533 66.148 C 164.637 64.61 165.685 63.498 167.137 63.498 Z M 169.679 69.444 C 169.399 70.358 168.486 70.961 167.282 70.961 C 165.592 70.961 164.513 69.776 164.513 68.009 L 164.513 67.853 L 172.199 67.853 L 172.199 67.011 C 172.199 63.664 170.249 61.491 167.127 61.491 C 163.953 61.491 161.94 63.789 161.94 67.271 C 161.94 70.785 163.932 72.957 167.231 72.957 C 169.834 72.957 171.764 71.523 172.096 69.444 Z M 177.884 70.961 C 176.764 70.961 176.017 70.39 176.017 69.485 C 176.017 68.612 176.733 68.051 177.978 67.968 L 180.519 67.812 L 180.519 68.654 C 180.519 69.984 179.347 70.961 177.884 70.961 Z M 177.106 72.916 C 178.517 72.916 179.897 72.178 180.529 70.982 L 180.581 70.982 L 180.581 72.739 L 183.071 72.739 L 183.071 65.15 C 183.071 62.936 181.297 61.491 178.569 61.491 C 175.768 61.491 174.015 62.968 173.901 65.026 L 176.297 65.026 C 176.463 64.111 177.241 63.519 178.465 63.519 C 179.741 63.519 180.519 64.184 180.519 65.338 L 180.519 66.128 L 177.614 66.294 C 174.938 66.46 173.434 67.634 173.434 69.589 C 173.434 71.575 174.98 72.916 177.106 72.916 Z M 185.156 72.739 L 187.739 72.739 L 187.739 66.325 C 187.739 64.776 188.621 63.83 190.083 63.83 C 190.519 63.83 190.913 63.893 191.131 63.976 L 191.131 61.636 C 190.866 61.572 190.595 61.537 190.322 61.533 C 189.025 61.533 188.061 62.303 187.687 63.643 L 187.635 63.643 L 187.635 61.72 L 185.156 61.72 L 185.156 72.74 Z"
        fill="currentColor"
      ></path>
    </g>
  </svg>
);

export function FeaturesSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollingRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      setupScrollAnimations();
      setupInfiniteScroll();
    }, sectionRef);

    return () => ctx.revert();
  }, []);

  const setupScrollAnimations = () => {
    if (!containerRef.current) return;

    gsap.fromTo(
      containerRef.current,
      {
        y: 50,
        opacity: 0,
      },
      {
        y: 0,
        opacity: 1,
        duration: 1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: containerRef.current,
          start: "top 80%",
          toggleActions: "play none none reverse",
        },
      },
    );
  };

  const setupInfiniteScroll = () => {
    if (!scrollingRef.current) return;

    const scrollingContainer = scrollingRef.current;
    const singleSetWidth = 3 * (200 + 64);

    gsap.set(scrollingContainer, { x: 0 });

    gsap.to(scrollingContainer, {
      x: `-=${singleSetWidth}`,
      duration: 12,
      ease: "none",
      repeat: -1,
      modifiers: {
        x: gsap.utils.unitize((x) => parseFloat(x) % singleSetWidth),
      },
    });
  };

  const features = [
    { icon: <FeatureIcon1 /> },
    { icon: <FeatureIcon2 /> },
    { icon: <FeatureIcon3 /> },
  ];

  const duplicatedFeatures = [...features, ...features, ...features, ...features];

  return (
    <section ref={sectionRef} className="relative overflow-hidden">
      <div ref={containerRef} className="container mx-auto max-w-5xl px-4 pb-10 sm:px-0 sm:pb-20">
        <div className="relative h-40 overflow-hidden">
          <div className="from-background absolute top-0 left-0 z-10 h-full w-20 bg-gradient-to-r to-transparent" />
          <div className="from-background absolute top-0 right-0 z-10 h-full w-20 bg-gradient-to-l to-transparent" />
          <div
            ref={scrollingRef}
            className="flex h-full items-center gap-16"
            style={{ width: "max-content" }}
          >
            {duplicatedFeatures.map((feature, index) => (
              <div
                key={index}
                className="flex flex-col items-center justify-center space-y-4 text-center"
                style={{ minWidth: "200px", height: "160px" }}
              >
                {feature.icon}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
